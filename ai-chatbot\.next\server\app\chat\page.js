/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/chat/page";
exports.ids = ["app/chat/page"];
exports.modules = {

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CGPT%5Cai-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CGPT%5Cai-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?5bc9\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'chat',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/page.tsx */ \"(rsc)/./src/app/chat/page.tsx\")), \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/chat/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/chat/page\",\n        pathname: \"/chat\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CGPT%5Cai-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22variable%22%3A%22--font-inter%22%2C%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22variable%22%3A%22--font-jetbrains-mono%22%2C%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Ccomponents%5Cproviders%5CSessionProvider.tsx&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22variable%22%3A%22--font-inter%22%2C%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22variable%22%3A%22--font-jetbrains-mono%22%2C%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Ccomponents%5Cproviders%5CSessionProvider.tsx&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/react-hot-toast/dist/index.mjs */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\"));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/providers/SessionProvider.tsx */ \"(ssr)/./src/components/providers/SessionProvider.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q0dQVCU1Q2FpLWNoYXRib3QlNUNub2RlX21vZHVsZXMlNUNuZXh0JTVDZm9udCU1Q2dvb2dsZSU1Q3RhcmdldC5jc3MlM0YlN0IlMjJwYXRoJTIyJTNBJTIyc3JjJTVDJTVDYXBwJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmltcG9ydCUyMiUzQSUyMkludGVyJTIyJTJDJTIyYXJndW1lbnRzJTIyJTNBJTVCJTdCJTIydmFyaWFibGUlMjIlM0ElMjItLWZvbnQtaW50ZXIlMjIlMkMlMjJzdWJzZXRzJTIyJTNBJTVCJTIybGF0aW4lMjIlNUQlN0QlNUQlMkMlMjJ2YXJpYWJsZU5hbWUlMjIlM0ElMjJpbnRlciUyMiU3RCZtb2R1bGVzPUQlM0ElNUNHUFQlNUNhaS1jaGF0Ym90JTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJKZXRCcmFpbnNfTW9ubyUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnZhcmlhYmxlJTIyJTNBJTIyLS1mb250LWpldGJyYWlucy1tb25vJTIyJTJDJTIyc3Vic2V0cyUyMiUzQSU1QiUyMmxhdGluJTIyJTVEJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyamV0YnJhaW5zTW9ubyUyMiU3RCZtb2R1bGVzPUQlM0ElNUNHUFQlNUNhaS1jaGF0Ym90JTVDbm9kZV9tb2R1bGVzJTVDcmVhY3QtaG90LXRvYXN0JTVDZGlzdCU1Q2luZGV4Lm1qcyZtb2R1bGVzPUQlM0ElNUNHUFQlNUNhaS1jaGF0Ym90JTVDc3JjJTVDYXBwJTVDZ2xvYmFscy5jc3MmbW9kdWxlcz1EJTNBJTVDR1BUJTVDYWktY2hhdGJvdCU1Q3NyYyU1Q2NvbXBvbmVudHMlNUNwcm92aWRlcnMlNUNTZXNzaW9uUHJvdmlkZXIudHN4JnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxzTUFBd0c7QUFDeEciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1jaGF0Ym90Lz83NDlmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcR1BUXFxcXGFpLWNoYXRib3RcXFxcbm9kZV9tb2R1bGVzXFxcXHJlYWN0LWhvdC10b2FzdFxcXFxkaXN0XFxcXGluZGV4Lm1qc1wiKTtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRDpcXFxcR1BUXFxcXGFpLWNoYXRib3RcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxccHJvdmlkZXJzXFxcXFNlc3Npb25Qcm92aWRlci50c3hcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22variable%22%3A%22--font-inter%22%2C%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22JetBrains_Mono%22%2C%22arguments%22%3A%5B%7B%22variable%22%3A%22--font-jetbrains-mono%22%2C%22subsets%22%3A%5B%22latin%22%5D%7D%5D%2C%22variableName%22%3A%22jetbrainsMono%22%7D&modules=D%3A%5CGPT%5Cai-chatbot%5Cnode_modules%5Creact-hot-toast%5Cdist%5Cindex.mjs&modules=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp%5Cglobals.css&modules=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Ccomponents%5Cproviders%5CSessionProvider.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp%5Cchat%5Cpage.tsx&server=true!":
/*!********************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp%5Cchat%5Cpage.tsx&server=true! ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/chat/page.tsx */ \"(ssr)/./src/app/chat/page.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RCUzQSU1Q0dQVCU1Q2FpLWNoYXRib3QlNUNzcmMlNUNhcHAlNUNjaGF0JTVDcGFnZS50c3gmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktY2hhdGJvdC8/M2ZjNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXEdQVFxcXFxhaS1jaGF0Ym90XFxcXHNyY1xcXFxhcHBcXFxcY2hhdFxcXFxwYWdlLnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp%5Cchat%5Cpage.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Download_Menu_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Menu,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Menu_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Menu,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Menu_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Menu,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _components_chat_ChatMessage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/chat/ChatMessage */ \"(ssr)/./src/components/chat/ChatMessage.tsx\");\n/* harmony import */ var _components_chat_ChatInput__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/chat/ChatInput */ \"(ssr)/./src/components/chat/ChatInput.tsx\");\n/* harmony import */ var _components_chat_ChatSidebar__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/chat/ChatSidebar */ \"(ssr)/./src/components/chat/ChatSidebar.tsx\");\n/* harmony import */ var _components_chat_GuestBanner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/chat/GuestBanner */ \"(ssr)/./src/components/chat/GuestBanner.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction ChatPage() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [sessions, setSessions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [currentSessionId, setCurrentSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTyping, setIsTyping] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"light\");\n    const [isGuest, setIsGuest] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [guestRemainingRequests, setGuestRemainingRequests] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Check for guest mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const urlParams = new URLSearchParams(window.location.search);\n        const guestMode = urlParams.get(\"guest\") === \"true\";\n        setIsGuest(guestMode);\n        // Load guest messages from localStorage\n        if (guestMode) {\n            const savedMessages = localStorage.getItem(\"guestMessages\");\n            if (savedMessages) {\n                setMessages(JSON.parse(savedMessages));\n            }\n        }\n    }, []);\n    // Redirect if not authenticated and not in guest mode\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (status === \"unauthenticated\" && !isGuest) {\n            router.push(\"/auth/signin\");\n        }\n    }, [\n        status,\n        router,\n        isGuest\n    ]);\n    // Load user preferences and sessions\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (session?.user) {\n            loadUserPreferences();\n            loadSessions();\n        }\n    }, [\n        session\n    ]);\n    // Scroll to bottom when messages change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages,\n        isTyping\n    ]);\n    // Apply theme\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (theme === \"dark\") {\n            document.documentElement.classList.add(\"dark\");\n        } else {\n            document.documentElement.classList.remove(\"dark\");\n        }\n    }, [\n        theme\n    ]);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    const loadUserPreferences = async ()=>{\n        try {\n            const response = await fetch(\"/api/user/preferences\");\n            if (response.ok) {\n                const data = await response.json();\n                setTheme(data.preferences.theme);\n            }\n        } catch (error) {\n            console.error(\"Failed to load user preferences:\", error);\n        }\n    };\n    const loadSessions = async ()=>{\n        try {\n            const response = await fetch(\"/api/sessions\");\n            if (response.ok) {\n                const data = await response.json();\n                setSessions(data.sessions);\n            }\n        } catch (error) {\n            console.error(\"Failed to load sessions:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load chat history\");\n        }\n    };\n    const loadSession = async (sessionId)=>{\n        try {\n            const response = await fetch(`/api/sessions/${sessionId}`);\n            if (response.ok) {\n                const data = await response.json();\n                setMessages(data.messages.map((msg)=>({\n                        ...msg,\n                        timestamp: new Date(msg.timestamp)\n                    })));\n                setCurrentSessionId(sessionId);\n            }\n        } catch (error) {\n            console.error(\"Failed to load session:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to load chat session\");\n        }\n    };\n    const createNewChat = async ()=>{\n        setMessages([]);\n        setCurrentSessionId(null);\n        setSidebarOpen(false);\n    };\n    const sendMessage = async (content)=>{\n        if (!content.trim() || isLoading) return;\n        const userMessage = {\n            role: \"user\",\n            content,\n            timestamp: new Date()\n        };\n        const newMessages = [\n            ...messages,\n            userMessage\n        ];\n        setMessages(newMessages);\n        setIsLoading(true);\n        setIsTyping(true);\n        try {\n            let response;\n            if (isGuest) {\n                // Guest mode API call\n                response = await fetch(\"/api/chat/guest\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        message: content,\n                        conversationHistory: messages\n                    })\n                });\n            } else {\n                // Authenticated user API call\n                response = await fetch(\"/api/chat\", {\n                    method: \"POST\",\n                    headers: {\n                        \"Content-Type\": \"application/json\"\n                    },\n                    body: JSON.stringify({\n                        message: content,\n                        sessionId: currentSessionId\n                    })\n                });\n            }\n            if (!response.ok) {\n                const errorData = await response.json();\n                if (response.status === 429 && isGuest) {\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Rate limit reached! Sign up for unlimited access.\");\n                } else {\n                    throw new Error(errorData.error || \"Failed to send message\");\n                }\n                return;\n            }\n            const data = await response.json();\n            const assistantMessage = {\n                role: \"assistant\",\n                content: data.response,\n                timestamp: new Date()\n            };\n            const finalMessages = [\n                ...newMessages,\n                assistantMessage\n            ];\n            setMessages(finalMessages);\n            // Update guest remaining requests\n            if (isGuest && data.remaining !== undefined) {\n                setGuestRemainingRequests(data.remaining);\n            }\n            // Save guest messages to localStorage\n            if (isGuest) {\n                localStorage.setItem(\"guestMessages\", JSON.stringify(finalMessages));\n            }\n            // Update current session ID if it's a new chat (authenticated users only)\n            if (!isGuest && !currentSessionId && data.sessionId) {\n                setCurrentSessionId(data.sessionId);\n                loadSessions(); // Refresh sessions list\n            }\n        } catch (error) {\n            console.error(\"Failed to send message:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error(error instanceof Error ? error.message : \"Failed to send message\");\n        } finally{\n            setIsLoading(false);\n            setIsTyping(false);\n        }\n    };\n    const deleteSession = async (sessionId)=>{\n        try {\n            const response = await fetch(`/api/sessions/${sessionId}`, {\n                method: \"DELETE\"\n            });\n            if (response.ok) {\n                setSessions((prev)=>prev.filter((s)=>s._id !== sessionId));\n                if (currentSessionId === sessionId) {\n                    setMessages([]);\n                    setCurrentSessionId(null);\n                }\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Chat deleted successfully\");\n            }\n        } catch (error) {\n            console.error(\"Failed to delete session:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to delete chat\");\n        }\n    };\n    const renameSession = async (sessionId, newTitle)=>{\n        try {\n            const response = await fetch(`/api/sessions/${sessionId}`, {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    title: newTitle\n                })\n            });\n            if (response.ok) {\n                setSessions((prev)=>prev.map((s)=>s._id === sessionId ? {\n                            ...s,\n                            title: newTitle\n                        } : s));\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Chat renamed successfully\");\n            }\n        } catch (error) {\n            console.error(\"Failed to rename session:\", error);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"Failed to rename chat\");\n        }\n    };\n    const toggleTheme = async ()=>{\n        const newTheme = theme === \"light\" ? \"dark\" : \"light\";\n        setTheme(newTheme);\n        try {\n            await fetch(\"/api/user/preferences\", {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    theme: newTheme\n                })\n            });\n        } catch (error) {\n            console.error(\"Failed to update theme preference:\", error);\n        }\n    };\n    const exportChat = ()=>{\n        if (messages.length === 0) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.error(\"No messages to export\");\n            return;\n        }\n        const chatText = messages.map((msg)=>`${msg.role === \"user\" ? \"You\" : \"AI\"} (${msg.timestamp.toLocaleString()}): ${msg.content}`).join(\"\\n\\n\");\n        const blob = new Blob([\n            chatText\n        ], {\n            type: \"text/plain\"\n        });\n        const url = URL.createObjectURL(blob);\n        const a = document.createElement(\"a\");\n        a.href = url;\n        a.download = `chat-${new Date().toISOString().split(\"T\")[0]}.txt`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        URL.revokeObjectURL(url);\n        react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Chat exported successfully\");\n    };\n    const clearChat = ()=>{\n        if (confirm(\"Are you sure you want to clear this chat?\")) {\n            setMessages([]);\n            if (isGuest) {\n                localStorage.removeItem(\"guestMessages\");\n            }\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Chat cleared\");\n        }\n    };\n    if (status === \"loading\" && !isGuest) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center bg-gradient-to-br from-indigo-50 via-white to-cyan-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-4 border-indigo-200\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-32 w-32 border-4 border-indigo-600 border-t-transparent absolute top-0 left-0\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"h-8 w-8 text-indigo-600 animate-pulse\",\n                            fill: \"none\",\n                            viewBox: \"0 0 24 24\",\n                            stroke: \"currentColor\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 323,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 320,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 319,\n            columnNumber: 7\n        }, this);\n    }\n    if (!session && !isGuest) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gray-50 dark:bg-gray-900\",\n        children: [\n            !isGuest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatSidebar__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                sessions: sessions,\n                currentSessionId: currentSessionId || undefined,\n                onSessionSelect: loadSession,\n                onNewChat: createNewChat,\n                onDeleteSession: deleteSession,\n                onRenameSession: renameSession,\n                isOpen: sidebarOpen,\n                onToggle: ()=>setSidebarOpen(!sidebarOpen),\n                theme: theme,\n                onThemeToggle: toggleTheme\n            }, void 0, false, {\n                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 341,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 flex flex-col\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3\",\n                                    children: [\n                                        !isGuest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSidebarOpen(true),\n                                            className: \"lg:hidden p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Menu_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                size: 20\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-lg font-semibold text-gray-900 dark:text-gray-100\",\n                                                    children: isGuest ? \"Guest Chat\" : currentSessionId ? sessions.find((s)=>s._id === currentSessionId)?.title || \"Chat\" : \"New Chat\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, this),\n                                                isGuest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-500 dark:text-gray-400\",\n                                                    children: [\n                                                        guestRemainingRequests,\n                                                        \" messages remaining •\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                            href: \"/auth/signup\",\n                                                            className: \"text-indigo-600 hover:text-indigo-500 ml-1\",\n                                                            children: \"Sign up for unlimited\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        isGuest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"/auth/signup\",\n                                            className: \"bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200\",\n                                            children: \"Sign Up\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: exportChat,\n                                            disabled: messages.length === 0,\n                                            className: \"p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            title: \"Export chat\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Menu_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 404,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: clearChat,\n                                            disabled: messages.length === 0,\n                                            className: \"p-2 text-gray-500 hover:text-red-600 dark:text-gray-400 dark:hover:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed\",\n                                            title: \"Clear chat\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Menu_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto\",\n                        children: messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-full text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-md mx-auto p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4\",\n                                        children: isGuest ? \"Welcome, Guest!\" : \"Welcome to AI Chatbot\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 423,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                                        children: isGuest ? `You have ${guestRemainingRequests} free messages to try our AI assistant. Ask questions, get recommendations, or just chat!` : \"Start a conversation with our AI assistant. Ask questions, get recommendations, or just chat!\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 17\n                                    }, this),\n                                    isGuest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 p-4 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-lg border border-indigo-200 dark:border-indigo-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-indigo-700 dark:text-indigo-300 mb-2\",\n                                                children: \"\\uD83C\\uDF89 Enjoying the experience?\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: \"/auth/signup\",\n                                                className: \"inline-flex items-center gap-2 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200\",\n                                                children: \"Sign up for unlimited access\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 437,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 gap-3 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg text-blue-700 dark:text-blue-300\",\n                                                children: '\\uD83D\\uDCA1 Try asking: \"What\\'s the weather like today?\"'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-green-50 dark:bg-green-900/20 rounded-lg text-green-700 dark:text-green-300\",\n                                                children: '\\uD83C\\uDFAF Or: \"Help me plan my day\"'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 449,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg text-purple-700 dark:text-purple-300\",\n                                                children: '\\uD83E\\uDD16 Or: \"Tell me a joke\"'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 421,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-0\",\n                            children: [\n                                messages.map((message, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatMessage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        message: message\n                                    }, index, false, {\n                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 17\n                                    }, this)),\n                                isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatMessage__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    message: {\n                                        role: \"assistant\",\n                                        content: \"\",\n                                        timestamp: new Date()\n                                    },\n                                    isTyping: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    ref: messagesEndRef\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 459,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this),\n                    isGuest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_GuestBanner__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                        remainingMessages: guestRemainingRequests,\n                        maxMessages: 10\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_ChatInput__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        onSendMessage: sendMessage,\n                        disabled: isLoading || isGuest && guestRemainingRequests <= 0,\n                        placeholder: isGuest && guestRemainingRequests <= 0 ? \"Sign up to continue chatting...\" : isLoading ? \"AI is thinking...\" : \"Type your message...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 487,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 356,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 338,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/chat/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/ChatInput.tsx":
/*!*******************************************!*\
  !*** ./src/components/chat/ChatInput.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatInput)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Mic_Send_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,Send,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_Send_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,Send,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_Mic_Send_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Mic,Send,Square!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ChatInput({ onSendMessage, disabled = false, placeholder = \"Type your message...\" }) {\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recognition, setRecognition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const textareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Initialize speech recognition\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (\"webkitSpeechRecognition\" in window || \"SpeechRecognition\" in window) {\n            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;\n            const recognitionInstance = new SpeechRecognition();\n            recognitionInstance.continuous = false;\n            recognitionInstance.interimResults = false;\n            recognitionInstance.lang = \"en-US\";\n            recognitionInstance.onresult = (event)=>{\n                const transcript = event.results[0][0].transcript;\n                setMessage((prev)=>prev + transcript);\n                setIsRecording(false);\n            };\n            recognitionInstance.onerror = (event)=>{\n                console.error(\"Speech recognition error:\", event.error);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Speech recognition failed\");\n                setIsRecording(false);\n            };\n            recognitionInstance.onend = ()=>{\n                setIsRecording(false);\n            };\n            setRecognition(recognitionInstance);\n        }\n    }, []);\n    // Auto-resize textarea\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (textareaRef.current) {\n            textareaRef.current.style.height = \"auto\";\n            textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;\n        }\n    }, [\n        message\n    ]);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (message.trim() && !disabled) {\n            onSendMessage(message.trim());\n            setMessage(\"\");\n        }\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const toggleRecording = ()=>{\n        if (!recognition) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Speech recognition not supported\");\n            return;\n        }\n        if (isRecording) {\n            recognition.stop();\n            setIsRecording(false);\n        } else {\n            try {\n                recognition.start();\n                setIsRecording(true);\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Listening... Speak now\");\n            } catch (error) {\n                react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to start speech recognition\");\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"flex items-end gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: textareaRef,\n                                value: message,\n                                onChange: (e)=>setMessage(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: placeholder,\n                                disabled: disabled,\n                                rows: 1,\n                                className: \"w-full resize-none rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-3 pr-12 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed max-h-32 overflow-y-auto\",\n                                style: {\n                                    minHeight: \"48px\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, this),\n                            recognition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: toggleRecording,\n                                disabled: disabled,\n                                className: `absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-full transition-colors ${isRecording ? \"text-red-500 hover:bg-red-50 dark:hover:bg-red-900/20\" : \"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"} disabled:opacity-50 disabled:cursor-not-allowed`,\n                                title: isRecording ? \"Stop recording\" : \"Start voice input\",\n                                children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_Send_Square_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 30\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_Send_Square_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    size: 16\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                    lineNumber: 125,\n                                    columnNumber: 53\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        disabled: disabled || !message.trim(),\n                        className: \"flex-shrink-0 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 dark:disabled:bg-gray-600 text-white rounded-lg p-3 transition-colors disabled:cursor-not-allowed\",\n                        title: \"Send message\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mic_Send_Square_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            isRecording && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 flex items-center gap-2 text-sm text-red-500\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-2 h-2 bg-red-500 rounded-full animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    \"Recording... Click the microphone or speak to stop\"\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatInput.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/ChatInput.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/ChatMessage.tsx":
/*!*********************************************!*\
  !*** ./src/components/chat/ChatMessage.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_User_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,User,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_User_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,User,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bot.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_User_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,User,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_User_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,User,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-x.js\");\n/* harmony import */ var _barrel_optimize_names_Bot_Copy_User_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bot,Copy,User,Volume2,VolumeX!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/volume-2.js\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/./node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ChatMessage({ message, isTyping = false }) {\n    const [isSpeaking, setIsSpeaking] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const isUser = message.role === \"user\";\n    const copyToClipboard = async ()=>{\n        try {\n            await navigator.clipboard.writeText(message.content);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.success(\"Message copied to clipboard\");\n        } catch (error) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Failed to copy message\");\n        }\n    };\n    const speakMessage = ()=>{\n        if (\"speechSynthesis\" in window) {\n            if (isSpeaking) {\n                window.speechSynthesis.cancel();\n                setIsSpeaking(false);\n            } else {\n                const utterance = new SpeechSynthesisUtterance(message.content);\n                utterance.onstart = ()=>setIsSpeaking(true);\n                utterance.onend = ()=>setIsSpeaking(false);\n                utterance.onerror = ()=>{\n                    setIsSpeaking(false);\n                    react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Speech synthesis failed\");\n                };\n                window.speechSynthesis.speak(utterance);\n            }\n        } else {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_2__.toast.error(\"Speech synthesis not supported\");\n        }\n    };\n    const formatTime = (date)=>{\n        return new Intl.DateTimeFormat(\"en-US\", {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        }).format(date);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex gap-3 p-4 ${isUser ? \"flex-row-reverse\" : \"flex-row\"}`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${isUser ? \"bg-blue-500 text-white\" : \"bg-gray-200 text-gray-600 dark:bg-gray-700 dark:text-gray-300\"}`,\n                children: isUser ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_User_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 19\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_User_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    size: 16\n                }, void 0, false, {\n                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 40\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `flex-1 max-w-3xl ${isUser ? \"text-right\" : \"text-left\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `inline-block p-3 rounded-lg ${isUser ? \"bg-blue-500 text-white\" : \"bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-100\"}`,\n                        children: isTyping ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: \"0ms\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                                            lineNumber: 77,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: \"150ms\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: \"300ms\"\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm text-gray-500\",\n                                    children: \"AI is typing...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"whitespace-pre-wrap break-words\",\n                            children: message.content\n                        }, void 0, false, {\n                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    !isTyping && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `flex items-center gap-2 mt-2 text-xs text-gray-500 ${isUser ? \"justify-end\" : \"justify-start\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formatTime(message.timestamp)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: copyToClipboard,\n                                className: \"p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors\",\n                                title: \"Copy message\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_User_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    size: 12\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            !isUser && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: speakMessage,\n                                className: \"p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors\",\n                                title: isSpeaking ? \"Stop speaking\" : \"Read aloud\",\n                                children: isSpeaking ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_User_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    size: 12\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 31\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bot_Copy_User_Volume2_VolumeX_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    size: 12\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 55\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatMessage.tsx\",\n        lineNumber: 57,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/ChatMessage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/ChatSidebar.tsx":
/*!*********************************************!*\
  !*** ./src/components/chat/ChatSidebar.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Edit3,LogOut,MessageSquare,Moon,Plus,Settings,Sun,Trash2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Edit3,LogOut,MessageSquare,Moon,Plus,Settings,Sun,Trash2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Edit3,LogOut,MessageSquare,Moon,Plus,Settings,Sun,Trash2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Edit3,LogOut,MessageSquare,Moon,Plus,Settings,Sun,Trash2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Edit3,LogOut,MessageSquare,Moon,Plus,Settings,Sun,Trash2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pen-line.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Edit3,LogOut,MessageSquare,Moon,Plus,Settings,Sun,Trash2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Edit3,LogOut,MessageSquare,Moon,Plus,Settings,Sun,Trash2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Edit3,LogOut,MessageSquare,Moon,Plus,Settings,Sun,Trash2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Edit3,LogOut,MessageSquare,Moon,Plus,Settings,Sun,Trash2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Edit3,LogOut,MessageSquare,Moon,Plus,Settings,Sun,Trash2,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ChatSidebar({ sessions, currentSessionId, onSessionSelect, onNewChat, onDeleteSession, onRenameSession, isOpen, onToggle, theme, onThemeToggle }) {\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const [editingSessionId, setEditingSessionId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const startEditing = (sessionId, currentTitle)=>{\n        setEditingSessionId(sessionId);\n        setEditingTitle(currentTitle);\n    };\n    const saveEdit = ()=>{\n        if (editingSessionId && editingTitle.trim()) {\n            onRenameSession(editingSessionId, editingTitle.trim());\n            setEditingSessionId(null);\n            setEditingTitle(\"\");\n        }\n    };\n    const cancelEdit = ()=>{\n        setEditingSessionId(null);\n        setEditingTitle(\"\");\n    };\n    const handleDeleteSession = (sessionId)=>{\n        if (confirm(\"Are you sure you want to delete this chat?\")) {\n            onDeleteSession(sessionId);\n        }\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        const now = new Date();\n        const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\n        if (diffInHours < 24) {\n            return \"Today\";\n        } else if (diffInHours < 48) {\n            return \"Yesterday\";\n        } else if (diffInHours < 168) {\n            return `${Math.floor(diffInHours / 24)} days ago`;\n        } else {\n            return date.toLocaleDateString();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\",\n                onClick: onToggle\n            }, void 0, false, {\n                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `fixed left-0 top-0 h-full w-80 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 transform transition-transform duration-300 ease-in-out z-50 ${isOpen ? \"translate-x-0\" : \"-translate-x-full\"} lg:relative lg:translate-x-0`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-semibold text-gray-900 dark:text-gray-100\",\n                                children: \"AI Chatbot\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onToggle,\n                                className: \"lg:hidden p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onNewChat,\n                            className: \"w-full flex items-center gap-3 px-4 py-3 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    size: 20\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 13\n                                }, this),\n                                \"New Chat\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-y-auto px-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: sessions.map((chatSession)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `group relative flex items-center gap-3 px-3 py-2 rounded-lg cursor-pointer transition-colors ${currentSessionId === chatSession._id ? \"bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400\" : \"hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300\"}`,\n                                    onClick: ()=>onSessionSelect(chatSession._id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            size: 16,\n                                            className: \"flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 17\n                                        }, this),\n                                        editingSessionId === chatSession._id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: editingTitle,\n                                                    onChange: (e)=>setEditingTitle(e.target.value),\n                                                    onKeyDown: (e)=>{\n                                                        if (e.key === \"Enter\") saveEdit();\n                                                        if (e.key === \"Escape\") cancelEdit();\n                                                    },\n                                                    className: \"flex-1 bg-transparent border-b border-gray-300 dark:border-gray-600 focus:outline-none focus:border-blue-500\",\n                                                    autoFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        saveEdit();\n                                                    },\n                                                    className: \"p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        size: 12\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: (e)=>{\n                                                        e.stopPropagation();\n                                                        cancelEdit();\n                                                    },\n                                                    className: \"p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                        size: 12\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"truncate text-sm font-medium\",\n                                                            children: chatSession.title\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 dark:text-gray-400\",\n                                                            children: [\n                                                                formatDate(chatSession.updatedAt),\n                                                                \" • \",\n                                                                chatSession.messageCount,\n                                                                \" messages\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                            lineNumber: 188,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"opacity-0 group-hover:opacity-100 flex items-center gap-1 transition-opacity\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                startEditing(chatSession._id, chatSession.title);\n                                                            },\n                                                            className: \"p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded\",\n                                                            title: \"Rename chat\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                size: 12\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: (e)=>{\n                                                                e.stopPropagation();\n                                                                handleDeleteSession(chatSession._id);\n                                                            },\n                                                            className: \"p-1 hover:bg-red-100 dark:hover:bg-red-900/20 text-red-500 rounded\",\n                                                            title: \"Delete chat\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                size: 12\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                                lineNumber: 212,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                            lineNumber: 204,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true)\n                                    ]\n                                }, chatSession._id, true, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-gray-200 dark:border-gray-700 p-4 space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onThemeToggle,\n                                className: \"w-full flex items-center gap-3 px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\",\n                                children: [\n                                    theme === \"light\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 34\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 55\n                                    }, this),\n                                    theme === \"light\" ? \"Dark Mode\" : \"Light Mode\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"w-full flex items-center gap-3 px-3 py-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        size: 16\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, this),\n                            session?.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-2 border-t border-gray-200 dark:border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3 px-3 py-2 text-sm text-gray-600 dark:text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs\",\n                                                children: session.user.name?.[0]?.toUpperCase()\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"truncate\",\n                                                        children: session.user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"truncate text-xs\",\n                                                        children: session.user.email\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>(0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)(),\n                                        className: \"w-full flex items-center gap-3 px-3 py-2 text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20 rounded-lg transition-colors\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Edit3_LogOut_MessageSquare_Moon_Plus_Settings_Sun_Trash2_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                size: 16\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Sign Out\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\ChatSidebar.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/ChatSidebar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/chat/GuestBanner.tsx":
/*!*********************************************!*\
  !*** ./src/components/chat/GuestBanner.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GuestBanner)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Sparkles_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Sparkles,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Sparkles_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Sparkles,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Sparkles_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Sparkles,Users!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction GuestBanner({ remainingMessages, maxMessages }) {\n    const usedMessages = maxMessages - remainingMessages;\n    const progressPercentage = usedMessages / maxMessages * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border-t border-indigo-200 dark:border-indigo-700 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Sparkles_Users_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        className: \"h-5 w-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"font-semibold text-gray-900 dark:text-white\",\n                                            children: \"Guest Mode\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 dark:text-gray-300\",\n                                            children: [\n                                                remainingMessages,\n                                                \" of \",\n                                                maxMessages,\n                                                \" messages remaining\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                                            lineNumber: 27,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/auth/signin\",\n                                    className: \"text-indigo-600 hover:text-indigo-700 dark:text-indigo-400 dark:hover:text-indigo-300 font-medium text-sm transition-colors\",\n                                    children: \"Sign In\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/auth/signup\",\n                                    className: \"group bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Sparkles_Users_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                                            lineNumber: 44,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Sign Up Free\",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Sparkles_Users_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                            className: \"h-4 w-4 group-hover:translate-x-1 transition-transform\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-3\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-indigo-600 to-purple-600 h-2 rounded-full transition-all duration-300\",\n                        style: {\n                            width: `${progressPercentage}%`\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between text-xs text-gray-500 dark:text-gray-400\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Free trial\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                usedMessages,\n                                \"/\",\n                                maxMessages,\n                                \" used\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                remainingMessages <= 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-3 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-yellow-800 dark:text-yellow-200\",\n                        children: [\n                            \"⚠️ Only \",\n                            remainingMessages,\n                            \" messages left!\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: \"/auth/signup\",\n                                className: \"font-medium underline ml-1\",\n                                children: \"Sign up now\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 15\n                            }, this),\n                            \" for unlimited access.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\chat\\\\GuestBanner.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/chat/GuestBanner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\components\\\\providers\\\\SessionProvider.tsx\",\n        lineNumber: 11,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFNkU7QUFPOUQsU0FBU0EsZ0JBQWdCLEVBQUVFLFFBQVEsRUFBUztJQUN6RCxxQkFBTyw4REFBQ0QsNERBQXVCQTtrQkFBRUM7Ozs7OztBQUNuQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLWNoYXRib3QvLi9zcmMvY29tcG9uZW50cy9wcm92aWRlcnMvU2Vzc2lvblByb3ZpZGVyLnRzeD81YTgyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgU2Vzc2lvblByb3ZpZGVyIGFzIE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIH0gZnJvbSAnbmV4dC1hdXRoL3JlYWN0JztcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0JztcblxuaW50ZXJmYWNlIFByb3BzIHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU2Vzc2lvblByb3ZpZGVyKHsgY2hpbGRyZW4gfTogUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0QXV0aFNlc3Npb25Qcm92aWRlcj57Y2hpbGRyZW59PC9OZXh0QXV0aFNlc3Npb25Qcm92aWRlcj47XG59XG4iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/providers/SessionProvider.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4b5d95dd0844\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktY2hhdGJvdC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MDA4NSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjRiNWQ5NWRkMDg0NFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\GPT\ai-chatbot\src\app\chat\page.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"variable\":\"--font-jetbrains-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-jetbrains-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/providers/SessionProvider */ \"(rsc)/./src/components/providers/SessionProvider.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(rsc)/./node_modules/react-hot-toast/dist/index.mjs\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"AI Chatbot - Intelligent Conversations\",\n    description: \"A modern AI chatbot with voice support, multiple tones, and multi-language capabilities\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_JetBrains_Mono_arguments_variable_font_jetbrains_mono_subsets_latin_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_SessionProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: [\n                    children,\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                        position: \"top-right\",\n                        toastOptions: {\n                            duration: 4000,\n                            style: {\n                                background: \"#363636\",\n                                color: \"#fff\"\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 32,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 29,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\GPT\\\\ai-chatbot\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/providers/SessionProvider.tsx":
/*!******************************************************!*\
  !*** ./src/components/providers/SessionProvider.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   $$typeof: () => (/* binding */ $$typeof),
/* harmony export */   __esModule: () => (/* binding */ __esModule),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\GPT\ai-chatbot\src\components\providers\SessionProvider.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;


/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (__default__);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9haS1jaGF0Ym90Ly4vc3JjL2FwcC9mYXZpY29uLmljbz8wYzJkIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/react-hot-toast","vendor-chunks/goober","vendor-chunks/lucide-react"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fchat%2Fpage&page=%2Fchat%2Fpage&appPaths=%2Fchat%2Fpage&pagePath=private-next-app-dir%2Fchat%2Fpage.tsx&appDir=D%3A%5CGPT%5Cai-chatbot%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CGPT%5Cai-chatbot&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();